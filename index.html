<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LohiaCorp - Modern Login</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- React and Babel CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite',
                        'shake': 'shake 0.5s ease-in-out',
                        'fadeInUp': 'fadeInUp 0.6s ease-out',
                        'slideInRight': 'slideInRight 0.6s ease-out',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        glow: {
                            '0%, 100%': { boxShadow: '0 0 20px rgba(241, 196, 15, 0.3)' },
                            '50%': { boxShadow: '0 0 30px rgba(241, 196, 15, 0.6)' },
                        },
                        shake: {
                            '0%, 100%': { transform: 'translateX(0)' },
                            '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-2px)' },
                            '20%, 40%, 60%, 80%': { transform: 'translateX(2px)' },
                        },
                        fadeInUp: {
                            'from': {
                                opacity: '0',
                                transform: 'translateY(20px)',
                            },
                            'to': {
                                opacity: '1',
                                transform: 'translateY(0)',
                            },
                        },
                        slideInRight: {
                            'from': {
                                opacity: '0',
                                transform: 'translateX(20px)',
                            },
                            'to': {
                                opacity: '1',
                                transform: 'translateX(0)',
                            },
                        },
                    },
                },
            },
        }
    </script>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
    
    <!-- Meta tags for SEO and social sharing -->
    <meta name="description" content="LohiaCorp - Secure and modern login portal with enhanced user experience">
    <meta name="keywords" content="LohiaCorp, login, secure, modern, enterprise">
    <meta name="author" content="LohiaCorp">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="LohiaCorp - Modern Login">
    <meta property="og:description" content="Secure and modern login portal">
    <meta property="og:type" content="website">
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
</head>
<body class="font-sans antialiased">
    <div id="root"></div>
    
    <!-- React Component Script -->
    <script type="text/babel" src="LoginPage.html"></script>
    
    <!-- Render the App -->
    <script type="text/babel">
        const { createRoot } = ReactDOM;
        const root = createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
    
    <!-- Service Worker for PWA capabilities (optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js').then(function(registration) {
                    console.log('ServiceWorker registration successful');
                }, function(err) {
                    console.log('ServiceWorker registration failed: ', err);
                });
            });
        }
    </script>
</body>
</html>
